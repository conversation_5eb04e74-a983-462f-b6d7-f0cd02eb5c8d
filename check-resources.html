<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .resource-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>网络资源本地化检查</h1>
    <div id="results"></div>

    <script>
        const resources = [
            { type: 'CSS', path: 'assets/css/font-awesome.min.css' },
            { type: 'JS', path: 'assets/js/tailwind.min.js' },
            { type: 'JS', path: 'assets/js/chart.umd.min.js' },
            { type: 'Image', path: 'assets/images/dashboard.jpg' },
            { type: 'Image', path: 'assets/images/process-1.jpg' },
            { type: 'Image', path: 'assets/images/process-2.jpg' },
            { type: 'Image', path: 'assets/images/process-3.jpg' },
            { type: 'Image', path: 'assets/images/process-4.jpg' },
            { type: 'Image', path: 'assets/images/process-5.jpg' }
        ];

        const resultsDiv = document.getElementById('results');

        function checkResource(resource) {
            return new Promise((resolve) => {
                if (resource.type === 'Image') {
                    const img = new Image();
                    img.onload = () => resolve({ ...resource, status: 'success', message: '图片加载成功' });
                    img.onerror = () => resolve({ ...resource, status: 'error', message: '图片加载失败' });
                    img.src = resource.path;
                } else {
                    fetch(resource.path)
                        .then(response => {
                            if (response.ok) {
                                resolve({ ...resource, status: 'success', message: '文件存在且可访问' });
                            } else {
                                resolve({ ...resource, status: 'error', message: `HTTP ${response.status}` });
                            }
                        })
                        .catch(error => {
                            resolve({ ...resource, status: 'error', message: error.message });
                        });
                }
            });
        }

        async function checkAllResources() {
            resultsDiv.innerHTML = '<p class="info">正在检查资源...</p>';
            
            const results = await Promise.all(resources.map(checkResource));
            
            resultsDiv.innerHTML = '';
            
            let successCount = 0;
            let errorCount = 0;
            
            results.forEach(result => {
                const div = document.createElement('div');
                div.className = 'resource-item';
                
                const statusClass = result.status === 'success' ? 'success' : 'error';
                const statusIcon = result.status === 'success' ? '✓' : '✗';
                
                if (result.status === 'success') successCount++;
                else errorCount++;
                
                div.innerHTML = `
                    <span class="${statusClass}">${statusIcon}</span>
                    <strong>${result.type}</strong>: ${result.path}
                    <br><small class="${statusClass}">${result.message}</small>
                `;
                
                resultsDiv.appendChild(div);
            });
            
            const summary = document.createElement('div');
            summary.style.marginTop = '20px';
            summary.style.padding = '15px';
            summary.style.backgroundColor = '#f5f5f5';
            summary.innerHTML = `
                <h3>检查结果汇总</h3>
                <p class="success">成功: ${successCount} 个资源</p>
                <p class="error">失败: ${errorCount} 个资源</p>
                <p><strong>本地化状态: ${errorCount === 0 ? '完全本地化 ✓' : '部分本地化 ⚠'}</strong></p>
            `;
            
            resultsDiv.appendChild(summary);
        }

        // 页面加载完成后开始检查
        window.addEventListener('load', checkAllResources);
    </script>
</body>
</html>
