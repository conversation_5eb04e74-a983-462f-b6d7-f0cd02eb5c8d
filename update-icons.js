// Font Awesome 图标更新脚本
const fs = require('fs');

// 读取HTML文件
let html = fs.readFileSync('indexnew.html', 'utf8');

// 图标映射表 - 从FA4到FA5/6
const iconMappings = {
  'fa fa-cutlery': 'fas fa-utensils',
  'fa fa-bars': 'fas fa-bars',
  'fa fa-search': 'fas fa-search',
  'fa fa-users': 'fas fa-users',
  'fa fa-file-text-o': 'far fa-file-alt',
  'fa fa-list': 'fas fa-list',
  'fa fa-shopping-cart': 'fas fa-shopping-cart',
  'fa fa-archive': 'fas fa-archive',
  'fa fa-search-minus': 'fas fa-search-minus',
  'fa fa-barcode': 'fas fa-barcode',
  'fa fa-line-chart': 'fas fa-chart-line',
  'fa fa-shield': 'fas fa-shield-alt',
  'fa fa-eye': 'fas fa-eye',
  'fa fa-cogs': 'fas fa-cogs',
  'fa fa-arrow-right': 'fas fa-arrow-right',
  'fa fa-envelope': 'fas fa-envelope',
  'fa fa-check': 'fas fa-check',
  'fa fa-map-marker': 'fas fa-map-marker-alt',
  'fa fa-phone': 'fas fa-phone',
  'fa fa-clock-o': 'far fa-clock',
  'fa fa-weixin': 'fab fa-weixin',
  'fa fa-weibo': 'fab fa-weibo',
  'fa fa-qq': 'fab fa-qq',
  'fa fa-paper-plane': 'fas fa-paper-plane'
};

// 执行替换
for (const [oldIcon, newIcon] of Object.entries(iconMappings)) {
  const regex = new RegExp(oldIcon.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'g');
  html = html.replace(regex, newIcon);
}

// 写回文件
fs.writeFileSync('indexnew.html', html, 'utf8');

console.log('图标更新完成！');
console.log('更新的图标数量:', Object.keys(iconMappings).length);
