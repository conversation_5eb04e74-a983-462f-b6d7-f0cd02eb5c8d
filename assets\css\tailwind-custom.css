/* 自定义Tailwind CSS样式 - 智慧食堂平台 */

/* 基础重置 */
*, ::before, ::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

::before, ::after {
  --tw-content: '';
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
}

body {
  margin: 0;
  line-height: inherit;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  text-decoration: underline dotted;
}

h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b, strong {
  font-weight: bolder;
}

code, kbd, samp, pre {
  font-family: ui-monospace, SFMono-Regular, "Roboto Mono", "Courier New", monospace;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button, input, optgroup, select, textarea {
  font-family: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button, [type='button'], [type='reset'], [type='submit'] {
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
  height: auto;
}

[type='search'] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::placeholder, textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

button, [role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img, svg, video, canvas, audio, iframe, embed, object {
  display: block;
  vertical-align: middle;
}

img, video {
  max-width: 100%;
  height: auto;
}

[hidden] {
  display: none;
}

/* 字体 */
.font-inter { font-family: 'Inter', system-ui, sans-serif; }
.font-code { font-family: 'JetBrains Mono', monospace; }

/* 颜色 */
.bg-dark-blue { background-color: #0B0E2F; }
.bg-dark { background-color: #1D2129; }
.bg-primary { background-color: #165DFF; }
.text-white { color: white; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-neon-blue { color: #00BFFF; }
.text-neon-green { color: #00FF9D; }
.text-neon-purple { color: #9D4EDD; }

/* 布局 */
.container { max-width: 1200px; margin: 0 auto; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-8 > * + * { margin-left: 2rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }

/* 网格 */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

/* 尺寸 */
.w-full { width: 100%; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-14 { width: 3.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-14 { height: 3.5rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-48 { height: 12rem; }

/* 内边距 */
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-24 { padding-top: 6rem; padding-bottom: 6rem; }

/* 外边距 */
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-10 { margin-bottom: 2.5rem; }
.mb-16 { margin-bottom: 4rem; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }

/* 定位 */
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.top-0 { top: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.z-10 { z-index: 10; }
.z-50 { z-index: 50; }

/* 文本 */
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.font-medium { font-weight: 500; }
.font-bold { font-weight: 700; }
.text-center { text-align: center; }
.leading-tight { line-height: 1.25; }

/* 边框 */
.border { border-width: 1px; }
.border-t { border-top-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-primary { border-color: #165DFF; }
.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

/* 阴影 */
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }

/* 过渡 */
.transition-all { transition: all 0.15s ease-in-out; }
.transition-colors { transition: color 0.15s ease-in-out; }
.duration-300 { transition-duration: 300ms; }

/* 变换 */
.transform { transform: translateZ(0); }
.-translate-y-1 { transform: translateY(-0.25rem); }

/* 溢出 */
.overflow-hidden { overflow: hidden; }
.overflow-x-hidden { overflow-x: hidden; }

/* 显示 */
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

/* 响应式 */
@media (min-width: 768px) {
  .md\\:flex { display: flex; }
  .md\\:hidden { display: none; }
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:flex-row { flex-direction: row; }
  .md\\:w-1\\/2 { width: 50%; }
  .md\\:w-12 { width: 3rem; }
  .md\\:h-20 { height: 5rem; }
  .md\\:mb-0 { margin-bottom: 0; }
  .md\\:mt-0 { margin-top: 0; }
  .md\\:pt-32 { padding-top: 8rem; }
  .md\\:pb-24 { padding-bottom: 6rem; }
  .md\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .md\\:text-xl { font-size: 1.25rem; }
  .md\\:col-span-2 { grid-column: span 2 / span 2; }
}

@media (min-width: 1024px) {
  .lg\\:flex-row { flex-direction: row; }
  .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\\:w-1\\/2 { width: 50%; }
  .lg\\:mb-0 { margin-bottom: 0; }
  .lg\\:px-8 { padding-left: 2rem; padding-right: 2rem; }
}

/* 自定义样式 */
.bg-gradient-to-r { background: linear-gradient(to right, var(--tw-gradient-stops)); }
.bg-gradient-to-b { background: linear-gradient(to bottom, var(--tw-gradient-stops)); }
.from-primary { --tw-gradient-from: #165DFF; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(22, 93, 255, 0)); }
.to-neon-blue { --tw-gradient-to: #00BFFF; }
.to-dark-blue { --tw-gradient-to: #0B0E2F; }

.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur-md { backdrop-filter: blur(12px); }

.antialiased { -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }

/* 悬停效果 */
.hover\\:text-neon-blue:hover { color: #00BFFF; }
.hover\\:bg-white\\/10:hover { background-color: rgba(255, 255, 255, 0.1); }
.hover\\:bg-primary\\/30:hover { background-color: rgba(22, 93, 255, 0.3); }
.hover\\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }

/* 焦点效果 */
.focus\\:outline-none:focus { outline: none; }

/* 组合效果 */
.group:hover .group-hover\\:bg-primary\\/30 { background-color: rgba(22, 93, 255, 0.3); }

/* 透明度 */
.bg-dark\\/30 { background-color: rgba(29, 33, 41, 0.3); }
.bg-dark\\/50 { background-color: rgba(29, 33, 41, 0.5); }
.bg-primary\\/10 { background-color: rgba(22, 93, 255, 0.1); }
.bg-primary\\/20 { background-color: rgba(22, 93, 255, 0.2); }
.bg-primary\\/30 { background-color: rgba(22, 93, 255, 0.3); }
.border-primary\\/20 { border-color: rgba(22, 93, 255, 0.2); }
.border-primary\\/30 { border-color: rgba(22, 93, 255, 0.3); }

/* 动画 */
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: .5; }
}

/* 最大宽度 */
.max-w-xl { max-width: 36rem; }
.max-w-md { max-width: 28rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-5xl { max-width: 64rem; }
