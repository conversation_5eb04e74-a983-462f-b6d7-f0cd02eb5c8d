<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>校园餐智慧食堂平台 - 智能化食堂管理解决方案</title>
  <link href="assets/css/tailwind-custom.css" rel="stylesheet">
  <link href="node_modules/@fortawesome/fontawesome-free/css/all.min.css" rel="stylesheet">
  <script src="assets/js/chart.umd.min.js"></script>



  <style>
    /* 自定义样式 */
    .content-auto {
      content-visibility: auto;
    }
    .text-shadow {
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .card-hover {
      transition: all 0.3s ease;
    }
    .card-hover:hover {
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      transform: translateY(-0.25rem);
    }
    .section-padding {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }
    @media (min-width: 768px) {
      .section-padding {
        padding-top: 6rem;
        padding-bottom: 6rem;
      }
    }
    .bg-gradient-primary {
      background: linear-gradient(to right, #165DFF, #0D47A1);
    }
    .animate-float {
      animation: float 6s ease-in-out infinite;
    }
    .neon-glow {
      box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
    }
    .neon-text {
      text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
    }
    .data-pulse {
      animation: pulse 2s infinite;
    }
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
      100% { transform: translateY(0px); }
    }
    @keyframes pulse {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
      100% { transform: scale(1); opacity: 1; }
    }
    .bg-grid {
      background-image: linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
      background-size: 20px 20px;
    }
    .clip-path-slant {
      clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
    }

    /* 缺少的样式 */
    .text-xs { font-size: 0.75rem; }
    .w-2 { width: 0.5rem; }
    .h-2 { height: 0.5rem; }
    .w-60 { width: 15rem; }
    .h-60 { height: 15rem; }
    .w-64 { width: 16rem; }
    .h-64 { height: 16rem; }
    .w-80 { width: 20rem; }
    .h-80 { height: 20rem; }
    .-top-40 { top: -10rem; }
    .-left-40 { left: -10rem; }
    .-bottom-40 { bottom: -10rem; }
    .-right-40 { right: -10rem; }
    .-left-20 { left: -5rem; }
    .-right-20 { right: -5rem; }
    .-left-32 { left: -8rem; }
    .-right-32 { right: -8rem; }
    .top-1\/4 { top: 25%; }
    .bottom-1\/4 { bottom: 25%; }
    .top-1\/3 { top: 33.333333%; }
    .bottom-1\/3 { bottom: 33.333333%; }
    .left-1\/2 { left: 50%; }
    .transform { transform: translateZ(0); }
    .-translate-x-1\/2 { transform: translateX(-50%); }
    .blur-3xl { filter: blur(64px); }
    .opacity-50 { opacity: 0.5; }

    /* 响应式文本大小 */
    .text-\[clamp\(2rem\,5vw\,3\.5rem\)\] { font-size: clamp(2rem, 5vw, 3.5rem); }
    .text-\[clamp\(1\.8rem\,4vw\,2\.8rem\)\] { font-size: clamp(1.8rem, 4vw, 2.8rem); }

    /* 间距 */
    .gap-4 { gap: 1rem; }
    .space-y-12 > * + * { margin-top: 3rem; }
    .space-y-6 > * + * { margin-top: 1.5rem; }

    /* 边框和颜色 */
    .border-neon-blue\/30 { border-color: rgba(0, 191, 255, 0.3); }
    .border-neon-blue\/50 { border-color: rgba(0, 191, 255, 0.5); }
    .bg-neon-blue\/20 { background-color: rgba(0, 191, 255, 0.2); }
    .bg-neon-purple\/20 { background-color: rgba(157, 78, 221, 0.2); }
    .bg-neon-green\/20 { background-color: rgba(0, 255, 157, 0.2); }
    .bg-neon-blue\/10 { background-color: rgba(0, 191, 255, 0.1); }
    .bg-neon-purple\/10 { background-color: rgba(157, 78, 221, 0.1); }

    /* 渐变 */
    .bg-gradient-to-b { background: linear-gradient(to bottom, var(--tw-gradient-stops)); }
    .from-primary\/5 { --tw-gradient-from: rgba(22, 93, 255, 0.05); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(22, 93, 255, 0)); }
    .from-neon-blue { --tw-gradient-from: #00BFFF; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(0, 191, 255, 0)); }
    .to-neon-purple { --tw-gradient-to: #9D4EDD; }
    .from-dark-blue { --tw-gradient-from: #0B0E2F; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(11, 14, 47, 0)); }
    .to-dark { --tw-gradient-to: #1D2129; }

    /* 其他 */
    .order-1 { order: 1; }
    .order-2 { order: 2; }
    .order-3 { order: 3; }
    .md\\:order-2 { order: 2; }
    .md\\:order-3 { order: 3; }
    @media (min-width: 768px) {
      .md\\:order-2 { order: 2; }
      .md\\:order-3 { order: 3; }
      .md\\:text-right { text-align: right; }
      .md\\:pr-12 { padding-right: 3rem; }
      .md\\:pl-12 { padding-left: 3rem; }
      .md\\:p-12 { padding: 3rem; }
    }

    /* 焦点样式 */
    .focus\\:ring-2:focus { box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.5); }
    .focus\\:ring-neon-blue:focus { box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.5); }

    /* 悬停阴影 */
    .hover\\:shadow-neon-blue\/30:hover { box-shadow: 0 10px 15px -3px rgba(0, 191, 255, 0.3); }

    /* 背景透明度 */
    .bg-dark-blue\/80 { background-color: rgba(11, 14, 47, 0.8); }
    .bg-dark-blue\/95 { background-color: rgba(11, 14, 47, 0.95); }

    /* 其他缺少的样式 */
    .pt-24 { padding-top: 6rem; }
    .pb-16 { padding-bottom: 4rem; }
    .pb-24 { padding-bottom: 6rem; }
    .pt-8 { padding-top: 2rem; }
    .mt-8 { margin-top: 2rem; }
    .mt-12 { margin-top: 3rem; }
    .mb-16 { margin-bottom: 4rem; }
    .mb-5 { margin-bottom: 1.25rem; }
    .mb-1 { margin-bottom: 0.25rem; }
    .ml-1 { margin-left: 0.25rem; }
    .inline-block { display: inline-block; }
    .flex-wrap { flex-wrap: wrap; }
    .max-w-5xl { max-width: 64rem; }
    .lg\\:w-1\/2 { width: 50%; }
    .lg\\:mb-0 { margin-bottom: 0; }
    @media (min-width: 1024px) {
      .lg\\:w-1\/2 { width: 50%; }
      .lg\\:mb-0 { margin-bottom: 0; }
      .lg\\:flex-row { flex-direction: row; }
      .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    }
  </style>
</head>

<body class="font-inter bg-dark-blue text-white antialiased overflow-x-hidden">
  <!-- 背景网格 -->
  <div class="fixed inset-0 bg-grid z-0 opacity-50"></div>

  <!-- 导航栏 -->
  <header id="navbar" class="fixed w-full top-0 z-50 transition-all duration-300 bg-dark-blue/80 backdrop-blur-md border-b border-primary/20">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16 md:h-20">
        <div class="flex items-center">
          <a href="#" class="flex items-center space-x-2">
            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center neon-glow">
              <i class="fas fa-utensils text-white text-xl"></i>
            </div>
            <span class="text-xl font-bold text-white">智慧食堂<span class="text-neon-blue neon-text">平台</span></span>
          </a>
        </div>

        <!-- 桌面导航 -->
        <nav class="hidden md:flex space-x-8">
          <a href="#features" class="text-white hover:text-neon-blue transition-colors font-medium">核心功能</a>
          <a href="#advantages" class="text-white hover:text-neon-blue transition-colors font-medium">系统优势</a>
          <a href="#process" class="text-white hover:text-neon-blue transition-colors font-medium">管理流程</a>
          <a href="#contact" class="text-white hover:text-neon-blue transition-colors font-medium">联系我们</a>
        </nav>

        <!-- 移动端菜单按钮 -->
        <div class="md:hidden">
          <button id="menu-toggle" class="text-white hover:text-neon-blue focus:outline-none">
            <i class="fas fa-bars text-2xl"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端导航菜单 -->
    <div id="mobile-menu" class="md:hidden hidden bg-dark-blue/95 backdrop-blur-md border-t border-primary/20">
      <div class="container mx-auto px-4 py-3 space-y-3">
        <a href="#features" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">核心功能</a>
        <a href="#advantages" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">系统优势</a>
        <a href="#process" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">管理流程</a>
        <a href="#contact" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">联系我们</a>
      </div>
    </div>
  </header>

  <!-- 英雄区域 -->
  <section class="pt-24 md:pt-32 pb-16 md:pb-24 bg-gradient-to-b from-primary/5 to-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute -top-40 -left-40 w-80 h-80 bg-neon-blue/20 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-40 -right-40 w-80 h-80 bg-neon-purple/20 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="flex flex-col lg:flex-row items-center">
        <div class="lg:w-1/2 mb-10 lg:mb-0">
          <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight text-white mb-6">
            智慧食堂管理平台<br>
            <span class="text-neon-blue neon-text">全方位智能化解决方案</span>
          </h1>
          <p class="text-lg md:text-xl text-gray-300 mb-8 max-w-xl">
            致力于打造全方位智能化管理体系，实现食品安全可视化、可管控、可追溯，为校园食堂管理提供高效便捷的技术支持。
          </p>
          <div class="flex flex-wrap gap-4">
            <a href="#features" class="bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-8 py-3 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30 flex items-center neon-glow">
              了解核心功能
              <i class="fas fa-arrow-right ml-2"></i>
            </a>
            <a href="#contact" class="bg-transparent hover:bg-white/10 text-white border border-neon-blue/50 font-medium px-8 py-3 rounded-lg transition-all flex items-center">
              联系我们
              <i class="fas fa-envelope ml-2"></i>
            </a>
          </div>

          <!-- 数据指标 -->
          <div class="mt-12 grid grid-cols-3 gap-4">
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-blue font-code text-2xl font-bold">99.9%</p>
              <p class="text-sm text-gray-400">系统稳定性</p>
            </div>
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-green font-code text-2xl font-bold">80%</p>
              <p class="text-sm text-gray-400">管理效率提升</p>
            </div>
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-purple font-code text-2xl font-bold">100%</p>
              <p class="text-sm text-gray-400">食品溯源率</p>
            </div>
          </div>
        </div>
        <div class="lg:w-1/2 relative">
          <div class="relative z-10 animate-float">
            <div class="bg-dark/50 backdrop-blur-md rounded-2xl shadow-2xl border border-primary/30 overflow-hidden">
              <img src="assets/images/dashboard.jpg" alt="智慧食堂管理系统界面" class="w-full h-auto">
              <div class="p-4 border-t border-primary/20">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-neon-green/20 rounded-full flex items-center justify-center text-neon-green">
                      <i class="fas fa-check text-lg"></i>
                    </div>
                    <div>
                      <p class="text-sm font-medium text-white">食品安全</p>
                      <p class="text-xs text-gray-400">全程可追溯</p>
                    </div>
                  </div>
                  <div class="text-xs text-gray-400">实时监控中 <span class="inline-block w-2 h-2 bg-neon-green rounded-full animate-pulse ml-1"></span></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 数据仪表盘 -->
  <section class="py-12 bg-dark/50 relative">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="bg-dark/50 backdrop-blur-md rounded-xl border border-primary/20 p-6 shadow-lg">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <h3 class="text-xl font-bold text-white mb-4 md:mb-0">系统运行数据 <span class="text-neon-blue text-sm font-normal">实时更新</span></h3>
          <div class="flex space-x-3">
            <button class="px-3 py-1 bg-primary/20 text-white text-sm rounded hover:bg-primary/30 transition-colors">今日</button>
            <button class="px-3 py-1 bg-primary/10 text-gray-400 text-sm rounded hover:bg-primary/30 transition-colors">本周</button>
            <button class="px-3 py-1 bg-primary/10 text-gray-400 text-sm rounded hover:bg-primary/30 transition-colors">本月</button>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 图表1 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">食堂运营效率</h4>
            <div class="h-48">
              <canvas id="efficiencyChart"></canvas>
            </div>
          </div>

          <!-- 图表2 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">食品安全检测</h4>
            <div class="h-48">
              <canvas id="safetyChart"></canvas>
            </div>
          </div>

          <!-- 图表3 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">用户满意度</h4>
            <div class="h-48">
              <canvas id="satisfactionChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 核心功能 -->
  <section id="features" class="section-padding bg-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute top-1/4 -left-20 w-60 h-60 bg-neon-purple/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 -right-20 w-60 h-60 bg-neon-blue/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">八大智能化功能，全面保障食堂安全管理</h2>
        <p class="text-lg text-gray-300">
          我们的智慧食堂平台集成了多项先进功能，从食材采购到餐后服务，全方位提升食堂管理效率与食品安全
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 功能卡片1 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-search text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能检查系统</h3>
          <p class="text-gray-400">
            员工通过扫码上传食堂卫生状况、设备运行情况，管理员在线进行评价反馈，实时监控食堂运营状态
          </p>
        </div>

        <!-- 功能卡片2 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-users text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">家校共陪餐</h3>
          <p class="text-gray-400">
            邀请家长参与陪餐体验，提升食堂管理透明度，加强家校互动沟通，增强家长对食堂的信任度
          </p>
        </div>

        <!-- 功能卡片3 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="far fa-file-alt text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能日志生成</h3>
          <p class="text-gray-400">
            每日自动生成食堂工作日志，完整记录运营情况，基于数据进行分析，为管理决策提供依据
          </p>
        </div>

        <!-- 功能卡片4 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-list text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">灵活菜单管理</h3>
          <p class="text-gray-400">
            支持周菜单灵活安排与调整，可直接打印输出，一键导入菜单信息生成采购计划，提高工作效率
          </p>
        </div>

        <!-- 功能卡片5 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-shopping-cart text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能采购系统</h3>
          <p class="text-gray-400">
            提供供应商灵活选择功能，支持智能价格对比，依据采购需求自动生成采购单，简化采购流程
          </p>
        </div>

        <!-- 功能卡片6 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-archive text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">出入库管理</h3>
          <p class="text-gray-400">
            对出入库流程进行完整管理，自动生成台账报表，实时监控库存情况，确保库存管理精准高效
          </p>
        </div>

        <!-- 功能卡片7 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-search-minus text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">全程溯源</h3>
          <p class="text-gray-400">
            实现食品从源头到餐桌的全程可追溯，打造透明化供应链，明确安全责任到人
          </p>
        </div>

        <!-- 功能卡片8 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-barcode text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">一键式留样标签打印</h3>
          <p class="text-gray-400">
            规范食品留样流程，自动生成留样标签，确保食品安全管理符合规范要求
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- 系统优势 -->
  <section id="advantages" class="section-padding bg-gradient-to-b from-dark-blue to-dark relative overflow-hidden clip-path-slant">
    <!-- 背景装饰 -->
    <div class="absolute top-1/3 -left-40 w-80 h-80 bg-neon-blue/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/3 -right-40 w-80 h-80 bg-neon-purple/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">系统优势</h2>
        <p class="text-lg text-gray-300">
          我们的智慧食堂管理平台不仅功能强大，还具备多项核心优势，为校园食堂管理带来全方位提升
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 优势1 -->
        <div class="bg-dark/50 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-neon-blue/30 group">
          <div class="w-12 h-12 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue mb-4 group-hover:bg-neon-blue/30 transition-colors">
            <i class="fas fa-chart-line text-xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">数据分析决策</h3>
          <p class="text-gray-400">
            通过智能数据分析，助力管理者做出科学合理的决策，优化食堂运营效率
          </p>
        </div>

        <!-- 优势2 -->
        <div class="bg-dark/50 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-neon-blue/30 group">
          <div class="w-12 h-12 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue mb-4 group-hover:bg-neon-blue/30 transition-colors">
            <i class="fas fa-shield-alt text-xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">食品安全追溯</h3>
          <p class="text-gray-400">
            建立完善的追溯体系，全方位保障食品安全，让家长和师生更放心
          </p>
        </div>

        <!-- 优势3 -->
        <div class="bg-dark/50 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-neon-blue/30 group">
          <div class="w-12 h-12 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue mb-4 group-hover:bg-neon-blue/30 transition-colors">
            <i class="fas fa-eye text-xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">实时监控</h3>
          <p class="text-gray-400">
            对食堂运营各环节进行实时监控，及时发现问题并解决，防患于未然
          </p>
        </div>

        <!-- 优势4 -->
        <div class="bg-dark/50 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-neon-blue/30 group">
          <div class="w-12 h-12 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue mb-4 group-hover:bg-neon-blue/30 transition-colors">
            <i class="fas fa-cogs text-xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">流程优化</h3>
          <p class="text-gray-400">
            简化食堂管理流程，减少人工操作，提高工作效率，降低管理成本
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- 管理流程 -->
  <section id="process" class="section-padding bg-dark relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute top-1/4 -left-32 w-64 h-64 bg-neon-purple/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 -right-32 w-64 h-64 bg-neon-blue/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">智能化管理流程</h2>
        <p class="text-lg text-gray-300">
          我们的智慧食堂管理平台通过科学的流程设计，实现了从食材采购到餐桌服务的全流程智能化管理
        </p>
      </div>

      <div class="relative">
        <!-- 流程线 -->
        <div class="hidden md:block absolute left-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-neon-blue to-neon-purple transform -translate-x-1/2"></div>

        <!-- 流程步骤 -->
        <div class="space-y-12 relative">
          <!-- 步骤1 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-3">智能采购</h3>
              <p class="text-gray-400">基于历史数据和当前库存，系统自动生成采购计划，智能匹配优质供应商，实现高效采购流程。</p>
            </div>
            <div class="md:w-12 flex justify-center">
              <div class="w-12 h-12 bg-neon-blue rounded-full flex items-center justify-center shadow-lg z-10 text-white font-bold">1</div>
            </div>
            <div class="md:w-1/2 md:pl-12 mt-8 md:mt-0">
              <img src="assets/images/process-1.jpg" alt="智能采购" class="w-full h-auto rounded-lg shadow-lg">
            </div>
          </div>

          <!-- 步骤2 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 order-1 md:order-2 mt-8 md:mt-0">
              <img src="assets/images/process-2.jpg" alt="食材验收" class="w-full h-auto rounded-lg shadow-lg">
            </div>
            <div class="md:w-12 flex justify-center order-2">
              <div class="w-12 h-12 bg-neon-purple rounded-full flex items-center justify-center shadow-lg z-10 text-white font-bold">2</div>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3 mb-8 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-3">食材验收</h3>
              <p class="text-gray-400">通过扫码快速完成食材验收，记录食材来源、检验结果等信息，确保食材安全可追溯。</p>
            </div>
          </div>

          <!-- 步骤3 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-3">库存管理</h3>
              <p class="text-gray-400">实时监控库存状态，自动预警库存不足或过期食品，支持先进先出的库存管理原则。</p>
            </div>
            <div class="md:w-12 flex justify-center">
              <div class="w-12 h-12 bg-neon-green rounded-full flex items-center justify-center shadow-lg z-10 text-white font-bold">3</div>
            </div>
            <div class="md:w-1/2 md:pl-12 mt-8 md:mt-0">
              <img src="assets/images/process-3.jpg" alt="库存管理" class="w-full h-auto rounded-lg shadow-lg">
            </div>
          </div>

          <!-- 步骤4 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 order-1 md:order-2 mt-8 md:mt-0">
              <img src="assets/images/process-4.jpg" alt="餐饮制作" class="w-full h-auto rounded-lg shadow-lg">
            </div>
            <div class="md:w-12 flex justify-center order-2">
              <div class="w-12 h-12 bg-neon-blue rounded-full flex items-center justify-center shadow-lg z-10 text-white font-bold">4</div>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3 mb-8 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-3">餐饮制作</h3>
              <p class="text-gray-400">标准化食谱管理，智能安排厨师工作量，实时监控厨房操作规范，确保餐饮质量。</p>
            </div>
          </div>

          <!-- 步骤5 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-3">智能供餐</h3>
              <p class="text-gray-400">自助点餐、智能结算系统，减少排队时间，提高供餐效率，同时收集用户反馈数据。</p>
            </div>
            <div class="md:w-12 flex justify-center">
              <div class="w-12 h-12 bg-neon-purple rounded-full flex items-center justify-center shadow-lg z-10 text-white font-bold">5</div>
            </div>
            <div class="md:w-1/2 md:pl-12 mt-8 md:mt-0">
              <img src="assets/images/process-5.jpg" alt="智能供餐" class="w-full h-auto rounded-lg shadow-lg">
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 联系我们 -->
  <section id="contact" class="section-padding bg-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute -top-40 -right-40 w-80 h-80 bg-neon-purple/10 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-neon-blue/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="max-w-5xl mx-auto">
        <div class="bg-dark/50 backdrop-blur-sm rounded-2xl shadow-xl border border-primary/30 overflow-hidden">
          <div class="grid grid-cols-1 md:grid-cols-2">
            <div class="p-8 md:p-12">
              <h2 class="text-2xl font-bold text-white mb-6">联系我们</h2>
              <p class="text-gray-400 mb-8">
                如需了解更多关于智慧食堂管理平台的信息，或有任何疑问，请填写下方表单，我们将尽快与您联系。
              </p>

              <form class="space-y-6">
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-400 mb-1">姓名</label>
                  <input type="text" id="name" class="w-full px-4 py-3 bg-dark border border-primary/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-blue text-white" placeholder="请输入您的姓名">
                </div>

                <div>
                  <label for="email" class="block text-sm font-medium text-gray-400 mb-1">邮箱</label>
                  <input type="email" id="email" class="w-full px-4 py-3 bg-dark border border-primary/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-blue text-white" placeholder="请输入您的邮箱">
                </div>

                <div>
                  <label for="phone" class="block text-sm font-medium text-gray-400 mb-1">电话</label>
                  <input type="tel" id="phone" class="w-full px-4 py-3 bg-dark border border-primary/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-blue text-white" placeholder="请输入您的电话">
                </div>

                <div>
                  <label for="message" class="block text-sm font-medium text-gray-400 mb-1">留言</label>
                  <textarea id="message" rows="4" class="w-full px-4 py-3 bg-dark border border-primary/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-blue text-white" placeholder="请输入您的留言"></textarea>
                </div>

                <button type="submit" class="w-full bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-6 py-3 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30 neon-glow">
                  发送消息
                  <i class="fas fa-paper-plane ml-2"></i>
                </button>
              </form>
            </div>

            <div class="bg-primary/10 p-8 md:p-12">
              <div class="space-y-8">
                <div class="flex items-start space-x-4">
                  <div class="w-10 h-10 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue">
                    <i class="fas fa-map-marker-alt"></i>
                  </div>
                  <div>
                    <h4 class="text-white font-medium mb-1">地址</h4>
                    <p class="text-gray-400">北京市海淀区中关村大街1号</p>
                  </div>
                </div>

                <div class="flex items-start space-x-4">
                  <div class="w-10 h-10 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue">
                    <i class="fas fa-phone"></i>
                  </div>
                  <div>
                    <h4 class="text-white font-medium mb-1">电话</h4>
                    <p class="text-gray-400">************</p>
                  </div>
                </div>

                <div class="flex items-start space-x-4">
                  <div class="w-10 h-10 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue">
                    <i class="fas fa-envelope"></i>
                  </div>
                  <div>
                    <h4 class="text-white font-medium mb-1">邮箱</h4>
                    <p class="text-gray-400"><EMAIL></p>
                  </div>
                </div>

                <div class="flex items-start space-x-4">
                  <div class="w-10 h-10 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue">
                    <i class="far fa-clock"></i>
                  </div>
                  <div>
                    <h4 class="text-white font-medium mb-1">工作时间</h4>
                    <p class="text-gray-400">周一至周五 9:00-18:00</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 页脚 -->
  <footer class="bg-dark border-t border-primary/20 py-12">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div class="md:col-span-2">
          <div class="flex items-center space-x-2 mb-4">
            <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <i class="fas fa-utensils text-white"></i>
            </div>
            <span class="text-lg font-bold text-white">智慧食堂平台</span>
          </div>
          <p class="text-gray-400 mb-4 max-w-md">
            致力于为校园食堂提供全方位智能化管理解决方案，让食堂管理更高效、食品安全更可靠。
          </p>
          <div class="flex space-x-4">
            <a href="#" class="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center text-neon-blue hover:bg-primary/30 transition-colors">
              <i class="fab fa-weixin"></i>
            </a>
            <a href="#" class="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center text-neon-blue hover:bg-primary/30 transition-colors">
              <i class="fab fa-weibo"></i>
            </a>
            <a href="#" class="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center text-neon-blue hover:bg-primary/30 transition-colors">
              <i class="fab fa-qq"></i>
            </a>
          </div>
        </div>

        <div>
          <h4 class="text-white font-medium mb-4">产品功能</h4>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-neon-blue transition-colors">智能检查系统</a></li>
            <li><a href="#" class="hover:text-neon-blue transition-colors">家校共陪餐</a></li>
            <li><a href="#" class="hover:text-neon-blue transition-colors">智能日志生成</a></li>
            <li><a href="#" class="hover:text-neon-blue transition-colors">菜单管理</a></li>
          </ul>
        </div>

        <div>
          <h4 class="text-white font-medium mb-4">联系方式</h4>
          <ul class="space-y-2 text-gray-400">
            <li>电话：************</li>
            <li>邮箱：<EMAIL></li>
            <li>地址：北京市海淀区中关村大街1号</li>
          </ul>
        </div>
      </div>

      <div class="border-t border-primary/20 mt-8 pt-8 text-center">
        <p class="text-gray-400">
          © 2024 智慧食堂管理平台. 保留所有权利.
        </p>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    // 移动端菜单切换
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');

    menuToggle.addEventListener('click', () => {
      mobileMenu.classList.toggle('hidden');
    });

    // 导航栏滚动效果
    window.addEventListener('scroll', () => {
      const navbar = document.getElementById('navbar');
      if (window.scrollY > 50) {
        navbar.classList.add('bg-dark-blue/95');
      } else {
        navbar.classList.remove('bg-dark-blue/95');
      }
    });

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // 图表初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 效率图表
      const efficiencyCtx = document.getElementById('efficiencyChart').getContext('2d');
      new Chart(efficiencyCtx, {
        type: 'line',
        data: {
          labels: ['6:00', '8:00', '10:00', '12:00', '14:00', '16:00', '18:00'],
          datasets: [{
            label: '运营效率',
            data: [65, 78, 85, 95, 88, 82, 90],
            borderColor: '#00BFFF',
            backgroundColor: 'rgba(0, 191, 255, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: 'rgba(22, 93, 255, 0.1)'
              },
              ticks: {
                color: '#9CA3AF'
              }
            },
            x: {
              grid: {
                color: 'rgba(22, 93, 255, 0.1)'
              },
              ticks: {
                color: '#9CA3AF'
              }
            }
          }
        }
      });

      // 安全检测图表
      const safetyCtx = document.getElementById('safetyChart').getContext('2d');
      new Chart(safetyCtx, {
        type: 'doughnut',
        data: {
          labels: ['合格', '待检', '异常'],
          datasets: [{
            data: [85, 10, 5],
            backgroundColor: ['#00FF9D', '#00BFFF', '#FF6B6B'],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                color: '#9CA3AF',
                usePointStyle: true,
                padding: 15
              }
            }
          }
        }
      });

      // 满意度图表
      const satisfactionCtx = document.getElementById('satisfactionChart').getContext('2d');
      new Chart(satisfactionCtx, {
        type: 'bar',
        data: {
          labels: ['周一', '周二', '周三', '周四', '周五'],
          datasets: [{
            label: '满意度',
            data: [4.2, 4.5, 4.3, 4.7, 4.6],
            backgroundColor: 'rgba(157, 78, 221, 0.8)',
            borderColor: '#9D4EDD',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 5,
              grid: {
                color: 'rgba(22, 93, 255, 0.1)'
              },
              ticks: {
                color: '#9CA3AF'
              }
            },
            x: {
              grid: {
                color: 'rgba(22, 93, 255, 0.1)'
              },
              ticks: {
                color: '#9CA3AF'
              }
            }
          }
        }
      });
    });
  </script>
</body>
</html>
